// apps/web/app/api/cloudinary/images/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const folder = searchParams.get('folder') || 'content-spark-gallery';
    const maxResults = parseInt(searchParams.get('max_results') || '50');
    const nextCursor = searchParams.get('next_cursor');
    const tags = searchParams.get('tags')?.split(',').filter(Boolean);

    // Build search expression
    let expression = `folder:${folder}`;
    if (tags && tags.length > 0) {
      expression += ` AND tags:(${tags.join(' OR ')})`;
    }

    const result = await cloudinary.search
      .expression(expression)
      .sort_by('created_at', 'desc')
      .max_results(maxResults)
      .next_cursor(nextCursor)
      .with_field('context')
      .with_field('tags')
      .execute();

    return NextResponse.json({
      images: result.resources,
      next_cursor: result.next_cursor,
      total_count: result.total_count,
    });
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json(
      { error: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}