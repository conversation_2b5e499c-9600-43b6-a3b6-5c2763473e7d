{"extends": "../../packages/typescript-config/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/lib/*": ["lib/*"], "@/components/*": ["components/*"], "@boastitup/ui": ["../../packages/ui"], "@boastitup/hooks": ["../../packages/hooks"], "@boastitup/supabase/client": ["../../packages/supabase/client.ts"], "@boastitup/supabase/server": ["../../packages/supabase/server.ts"], "@boastitup/types": ["../../packages/types"], "@boastitup/auth": ["../../packages/auth"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}