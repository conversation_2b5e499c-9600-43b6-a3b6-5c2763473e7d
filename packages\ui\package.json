{"name": "@boastitup/ui", "version": "0.0.0", "main": "./index.ts", "types": "./index.ts", "license": "MIT", "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.356.0", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "^8.57.0", "react": "^18.2.0", "tailwindcss": "^3.4.1"}}